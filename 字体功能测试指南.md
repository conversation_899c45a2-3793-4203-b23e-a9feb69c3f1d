# 字体选择功能测试指南

## 功能状态 ✅

根据后端日志，字体选择功能已经正常工作：

- ✅ 楷体字体已成功注册
- ✅ HTML字体标签正确处理
- ✅ 字体映射功能正常
- ✅ PDF生成包含字体效果

## 测试步骤

### 1. 基础测试
1. 在编辑器中输入：`这段文字将变成楷体`
2. 用鼠标选择这段文字
3. 点击工具栏的字体按钮（T图标）
4. 选择"楷体"
5. 查看文本是否变成：`<span style="font-family: KaiTi">这段文字将变成楷体</span>`

### 2. 快捷键测试
1. 选择任意文本
2. 按 `Ctrl+Shift+F`
3. 选择字体
4. 查看效果

### 3. PDF预览测试
1. 输入包含字体标签的文本
2. 查看PDF预览
3. 确认字体效果正确显示

## 已验证的字体标签

以下HTML标签已确认可以正常工作：

```html
<span style="font-family: KaiTi">楷体文字</span>
<span style="font-family: SimHei">黑体文字</span>
<span style="font-family: SimSun">宋体文字</span>
<span style="font-family: Arial">Arial文字</span>
```

## 字体映射表

| 用户选择 | 实际字体 | 状态 |
|---------|---------|------|
| 楷体 (KaiTi) | KaiTi | ✅ 正常 |
| 黑体 (SimHei) | ChineseFont-Bold | ✅ 正常 |
| 宋体 (SimSun) | ChineseFont | ✅ 正常 |
| 微软雅黑 | ChineseFont | ✅ 正常 |
| Arial | Helvetica | ✅ 正常 |
| Times New Roman | Times-Roman | ✅ 正常 |

## 故障排除

如果字体选择没有效果：

1. **检查文本选择**：确保先选择文本再点击字体按钮
2. **检查HTML标签**：确认文本被正确包装在 `<span>` 标签中
3. **检查PDF预览**：字体效果只在PDF预览中显示，编辑器中不会显示
4. **刷新页面**：如果有问题，尝试刷新浏览器页面

## 成功示例

输入以下内容测试：

```markdown
# 字体测试

普通文字

<span style="font-family: KaiTi">这是楷体文字</span>

<span style="font-family: SimHei">这是黑体文字</span>

<span style="font-family: Arial">This is Arial text</span>
```

在PDF预览中，您应该看到不同的字体效果。
