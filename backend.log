INFO:     Will watch for changes in these directories: ['/Users/<USER>/Documents/augment-projects/PrintMind/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [5556] using WatchFiles
INFO:     Started server process [5560]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:64658 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:64865 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:64992 - "GET /api/layout/presets HTTP/1.1" 200 OK
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1588 extra bytes in post.stringData array
cmap subtable is reported as having zero length: platformID 1, platEncID 0, format 0 offset 20. Skipping table.
2 extra bytes in post.stringData array
INFO:     127.0.0.1:64993 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:64992 - "OPTIONS /api/pdf/preview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65180 - "OPTIONS /api/documents/443b8cf7-640a-49ff-928e-f18af2efe88f HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:64992 - "DELETE /api/documents/443b8cf7-640a-49ff-928e-f18af2efe88f HTTP/1.1" 200 OK
INFO:     127.0.0.1:65180 - "POST /api/pdf/preview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65180 - "OPTIONS /api/documents/ef64b6e5-722d-4771-9ddf-e1c2d4b4f4a1 HTTP/1.1" 200 OK
INFO:     127.0.0.1:65180 - "DELETE /api/documents/ef64b6e5-722d-4771-9ddf-e1c2d4b4f4a1 HTTP/1.1" 200 OK
INFO:     127.0.0.1:65180 - "OPTIONS /api/documents/8163ae74-3e66-448b-be92-a3221319f304 HTTP/1.1" 200 OK
INFO:     127.0.0.1:65180 - "DELETE /api/documents/8163ae74-3e66-448b-be92-a3221319f304 HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:65180 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:65180 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:65180 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:65180 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:65180 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
INFO:     127.0.0.1:65180 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:65180 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:65446 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:65446 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:49364 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:49364 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:49364 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [5560]
INFO:     Started server process [5827]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [5827]
INFO:     Started server process [5836]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [5836]
INFO:     Started server process [5846]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:53514 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:53514 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:53581 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [5846]
INFO:     Started server process [6661]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:61862 - "OPTIONS /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:61862 - "POST /api/pdf/preview HTTP/1.1" 500 Internal Server Error
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:61862 - "POST /api/pdf/preview HTTP/1.1" 500 Internal Server Error
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:61862 - "POST /api/pdf/preview HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [6661]
INFO:     Started server process [6690]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:62552 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [6690]
INFO:     Started server process [6702]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:62716 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [6702]
INFO:     Started server process [6720]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:62928 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [6720]
INFO:     Started server process [6753]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:63201 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [6753]
INFO:     Started server process [6773]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:63483 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [6773]
INFO:     Started server process [6792]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:63621 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [6792]
INFO:     Started server process [6988]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [6988]
INFO:     Started server process [7043]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [7043]
INFO:     Started server process [7119]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [7119]
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Started server process [7137]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [7137]
INFO:     Started server process [7151]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [7151]
INFO:     Started server process [7167]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [7167]
INFO:     Started server process [7183]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [7183]
INFO:     Started server process [7191]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:53906 - "OPTIONS /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:53906 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [7191]
INFO:     Started server process [7405]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [7405]
INFO:     Started server process [7466]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:55252 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [7466]
INFO:     Started server process [7982]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [7982]
INFO:     Started server process [7991]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [7991]
INFO:     Started server process [8003]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [8003]
INFO:     Started server process [8011]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [8011]
INFO:     Started server process [8019]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:63983 - "OPTIONS /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:63983 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [8019]
INFO:     Started server process [8453]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:53412 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [8453]
INFO:     Started server process [8469]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:53592 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [8469]
INFO:     Started server process [8496]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:53831 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:53897 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:53897 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:53981 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:54067 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:54099 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:54099 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:54246 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:54365 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:54418 - "POST /api/pdf/preview HTTP/1.1" 200 OK
INFO:     127.0.0.1:54586 - "OPTIONS /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:54586 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [8496]
INFO:     Started server process [8587]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:54787 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [8587]
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Started server process [8622]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [8622]
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Started server process [8786]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [8786]
INFO:     Started server process [8850]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:58157 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [8850]
INFO:     Started server process [9037]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:61032 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:61224 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [9037]
INFO:     Started server process [9085]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:61564 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:61810 - "POST /api/pdf/preview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63352 - "OPTIONS /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:63352 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:63352 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:50174 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:50174 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:50500 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:50594 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:51179 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:51300 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:51300 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:51300 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:51300 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:51300 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:51300 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:52099 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:52463 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:52463 - "POST /api/pdf/preview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52463 - "POST /api/pdf/preview HTTP/1.1" 422 Unprocessable Entity
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:52463 - "POST /api/pdf/preview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52682 - "GET /api/layout/presets HTTP/1.1" 200 OK
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1588 extra bytes in post.stringData array
cmap subtable is reported as having zero length: platformID 1, platEncID 0, format 0 offset 20. Skipping table.
2 extra bytes in post.stringData array
INFO:     127.0.0.1:52684 - "GET /api/fonts/list HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode字体失败: "don't know anything about CID font STSongStd-Light"
使用Helvetica作为备选字体
INFO:     127.0.0.1:52682 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [9085]
INFO:     Started server process [9760]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [9760]
INFO:     Started server process [9777]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [9777]
INFO:     Started server process [9795]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [9795]
INFO:     Started server process [9811]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [9811]
INFO:     Started server process [9847]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/font_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [9847]
INFO:     Started server process [9874]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/font_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [9874]
INFO:     Started server process [9890]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:60560 - "GET /api/layout/presets HTTP/1.1" 200 OK
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1 extra bytes in post.stringData array
1588 extra bytes in post.stringData array
cmap subtable is reported as having zero length: platformID 1, platEncID 0, format 0 offset 20. Skipping table.
2 extra bytes in post.stringData array
INFO:     127.0.0.1:60562 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:60560 - "OPTIONS /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60560 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60560 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60560 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60770 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60843 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/font_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [9890]
Process SpawnProcess-46:
Traceback (most recent call last):
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 315, in _bootstrap
    self.run()
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1030, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1007, in _find_and_load
  File "<frozen importlib._bootstrap>", line 986, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 680, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 850, in exec_module
  File "<frozen importlib._bootstrap>", line 228, in _call_with_frames_removed
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/main.py", line 11, in <module>
    from app.api import documents, layout, pdf, fonts
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/api/fonts.py", line 7, in <module>
    from app.services.font_service import FontService
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 7, in <module>
    class FontService:
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 13, in FontService
    def get_available_fonts(self) -> List[FontInfo]:
NameError: name 'FontInfo' is not defined
WARNING:  WatchFiles detected changes in 'app/services/font_service.py'. Reloading...
Process SpawnProcess-47:
Traceback (most recent call last):
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 315, in _bootstrap
    self.run()
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1030, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1007, in _find_and_load
  File "<frozen importlib._bootstrap>", line 986, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 680, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 850, in exec_module
  File "<frozen importlib._bootstrap>", line 228, in _call_with_frames_removed
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/main.py", line 11, in <module>
    from app.api import documents, layout, pdf, fonts
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/api/fonts.py", line 7, in <module>
    from app.services.font_service import FontService
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 7, in <module>
    class FontService:
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 95, in FontService
    def get_chinese_fonts(self) -> List[FontInfo]:
NameError: name 'FontInfo' is not defined
WARNING:  WatchFiles detected changes in 'app/services/font_service.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/font_service.py'. Reloading...
Process SpawnProcess-49:
Traceback (most recent call last):
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 315, in _bootstrap
    self.run()
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1030, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1007, in _find_and_load
  File "<frozen importlib._bootstrap>", line 986, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 680, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 850, in exec_module
  File "<frozen importlib._bootstrap>", line 228, in _call_with_frames_removed
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/main.py", line 11, in <module>
    from app.api import documents, layout, pdf, fonts
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/api/fonts.py", line 7, in <module>
    from app.services.font_service import FontService
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 7, in <module>
    class FontService:
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 94, in FontService
    def get_font_info(self, font_name: str) -> Dict[str, Any] | None:
TypeError: unsupported operand type(s) for |: '_GenericAlias' and 'NoneType'
WARNING:  WatchFiles detected changes in 'app/services/font_service.py'. Reloading...
Process SpawnProcess-50:
Traceback (most recent call last):
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 315, in _bootstrap
    self.run()
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1030, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1007, in _find_and_load
  File "<frozen importlib._bootstrap>", line 986, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 680, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 850, in exec_module
  File "<frozen importlib._bootstrap>", line 228, in _call_with_frames_removed
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/main.py", line 11, in <module>
    from app.api import documents, layout, pdf, fonts
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/api/fonts.py", line 7, in <module>
    from app.services.font_service import FontService
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 7, in <module>
    class FontService:
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 94, in FontService
    def get_font_info(self, font_name: str) -> Dict[str, Any] | None:
TypeError: unsupported operand type(s) for |: '_GenericAlias' and 'NoneType'
WARNING:  WatchFiles detected changes in 'app/api/fonts.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/fonts.py'. Reloading...
Process SpawnProcess-52:
Traceback (most recent call last):
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 315, in _bootstrap
    self.run()
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1030, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1007, in _find_and_load
  File "<frozen importlib._bootstrap>", line 986, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 680, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 850, in exec_module
  File "<frozen importlib._bootstrap>", line 228, in _call_with_frames_removed
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/main.py", line 11, in <module>
    from app.api import documents, layout, pdf, fonts
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/api/fonts.py", line 6, in <module>
    from app.services.font_service import FontService
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 7, in <module>
    class FontService:
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 94, in FontService
    def get_font_info(self, font_name: str) -> Dict[str, Any] | None:
TypeError: unsupported operand type(s) for |: '_GenericAlias' and 'NoneType'
WARNING:  WatchFiles detected changes in 'app/api/fonts.py'. Reloading...
Process SpawnProcess-53:
Traceback (most recent call last):
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 315, in _bootstrap
    self.run()
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1030, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1007, in _find_and_load
  File "<frozen importlib._bootstrap>", line 986, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 680, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 850, in exec_module
  File "<frozen importlib._bootstrap>", line 228, in _call_with_frames_removed
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/main.py", line 11, in <module>
    from app.api import documents, layout, pdf, fonts
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/api/fonts.py", line 6, in <module>
    from app.services.font_service import FontService
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 7, in <module>
    class FontService:
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 94, in FontService
    def get_font_info(self, font_name: str) -> Dict[str, Any] | None:
TypeError: unsupported operand type(s) for |: '_GenericAlias' and 'NoneType'
WARNING:  WatchFiles detected changes in 'app/models/schemas.py'. Reloading...
Process SpawnProcess-54:
Traceback (most recent call last):
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 315, in _bootstrap
    self.run()
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1030, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1007, in _find_and_load
  File "<frozen importlib._bootstrap>", line 986, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 680, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 850, in exec_module
  File "<frozen importlib._bootstrap>", line 228, in _call_with_frames_removed
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/main.py", line 11, in <module>
    from app.api import documents, layout, pdf, fonts
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/api/fonts.py", line 6, in <module>
    from app.services.font_service import FontService
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 7, in <module>
    class FontService:
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 94, in FontService
    def get_font_info(self, font_name: str) -> Dict[str, Any] | None:
TypeError: unsupported operand type(s) for |: '_GenericAlias' and 'NoneType'
WARNING:  WatchFiles detected changes in 'app/services/font_service.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/font_service.py'. Reloading...
Process SpawnProcess-56:
Traceback (most recent call last):
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 315, in _bootstrap
    self.run()
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1030, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1007, in _find_and_load
  File "<frozen importlib._bootstrap>", line 986, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 680, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 850, in exec_module
  File "<frozen importlib._bootstrap>", line 228, in _call_with_frames_removed
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/main.py", line 11, in <module>
    from app.api import documents, layout, pdf, fonts
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/api/fonts.py", line 6, in <module>
    from app.services.font_service import FontService
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 7, in <module>
    class FontService:
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/font_service.py", line 94, in FontService
    def get_font_info(self, font_name: str) -> Dict[str, Any] | None:
TypeError: unsupported operand type(s) for |: '_GenericAlias' and 'NoneType'
WARNING:  WatchFiles detected changes in 'app/services/font_service.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/font_service.py'. Reloading...
INFO:     Started server process [10610]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:64686 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:64692 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:65035 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:65037 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:50934 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:50935 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:52173 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:52174 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:57832 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:57834 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:57989 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:57990 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:58313 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:58315 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:58969 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:58970 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:59355 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:59358 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:59644 - "OPTIONS /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:59644 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:59755 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:59755 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:59755 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:59755 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60914 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60914 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60914 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60914 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60914 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60914 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:61253 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:61253 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:61253 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:61253 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:61253 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:61253 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:61253 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:61804 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:62126 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:62339 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:62461 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:62461 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:62461 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:62714 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:62836 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
INFO:     127.0.0.1:62836 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
INFO:     127.0.0.1:63268 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:63268 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:63568 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:63568 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:63568 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:64072 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:64140 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:64140 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:64140 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:64491 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:64491 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:64725 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:64896 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:65032 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:65032 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:65194 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:65310 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:65460 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
INFO:     127.0.0.1:65460 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
INFO:     127.0.0.1:49258 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
INFO:     127.0.0.1:49338 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
INFO:     127.0.0.1:49338 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
INFO:     127.0.0.1:49338 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:49338 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:49662 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:49662 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:50039 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:50123 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:50246 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:50246 - "POST /api/pdf/preview HTTP/1.1" 200 OK
INFO:     127.0.0.1:57000 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:57002 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:50009 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:50012 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:60489 - "OPTIONS /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60489 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60562 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60562 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60562 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60562 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60711 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60814 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60814 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60814 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:60814 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
本地图片文件未找到: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAZElEQVRYCe3YsQoAIAhFUf//p21Ip7jQpsMNIniTHN9UREQuuguHyeFT27kyw7N0VRzmWYRrekgqUEYZEqDczihDApTbGWVIgHI7owwJUG5nlCEByu2MMiRAuZ35kimm/poYeQ8iGLkoqyFDwQAAAABJRU5ErkJggg==
INFO:     127.0.0.1:50687 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:50687 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:51237 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:52163 - "POST /api/pdf/preview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52270 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:52273 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:52460 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:52461 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:52625 - "OPTIONS /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:52625 - "POST /api/pdf/preview HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/models/schemas.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [10610]
INFO:     Started server process [46263]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:52805 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:52805 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:52805 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:53003 - "POST /api/pdf/preview HTTP/1.1" 200 OK
成功注册中文字体: /System/Library/Fonts/STHeiti Light.ttc
成功注册粗体字体: /System/Library/Fonts/STHeiti Medium.ttc
成功注册楷体字体: /Users/<USER>/Library/Fonts/simkai.ttf
未找到合适的系统中文字体，使用内置Unicode字体
注册内置Unicode CID字体成功
粗体字体不可用，将使用普通字体
楷体CID字体不可用，将使用宋体作为楷体备选
INFO:     127.0.0.1:53003 - "POST /api/pdf/preview HTTP/1.1" 200 OK
INFO:     127.0.0.1:56590 - "GET /api/fonts/list HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [46263]
INFO:     Started server process [47889]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [47889]
INFO:     Started server process [47908]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [47908]
INFO:     Started server process [47924]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:59223 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:59229 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:59223 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:59223 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:61757 - "OPTIONS /api/pdf/preview HTTP/1.1" 400 Bad Request
INFO:     127.0.0.1:61757 - "OPTIONS /api/pdf/preview HTTP/1.1" 400 Bad Request
INFO:     127.0.0.1:61757 - "OPTIONS /api/pdf/preview HTTP/1.1" 400 Bad Request
INFO:     127.0.0.1:61757 - "GET /api/layout/presets HTTP/1.1" 200 OK
INFO:     127.0.0.1:61858 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:61757 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:61757 - "GET /api/fonts/list HTTP/1.1" 200 OK
INFO:     127.0.0.1:61757 - "OPTIONS /api/pdf/preview HTTP/1.1" 400 Bad Request
INFO:     127.0.0.1:61757 - "OPTIONS /api/pdf/preview HTTP/1.1" 400 Bad Request
INFO:     127.0.0.1:62836 - "OPTIONS /api/pdf/preview HTTP/1.1" 400 Bad Request
INFO:     127.0.0.1:62836 - "OPTIONS /api/pdf/preview HTTP/1.1" 400 Bad Request
INFO:     127.0.0.1:62836 - "OPTIONS /api/pdf/preview HTTP/1.1" 400 Bad Request
INFO:     127.0.0.1:62836 - "OPTIONS /api/pdf/preview HTTP/1.1" 400 Bad Request
INFO:     127.0.0.1:62836 - "OPTIONS /api/pdf/preview HTTP/1.1" 400 Bad Request
INFO:     127.0.0.1:62836 - "OPTIONS /api/pdf/preview HTTP/1.1" 400 Bad Request
