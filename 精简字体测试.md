# 精简字体选择功能测试

## 可用字体列表

现在只保留以下5种字体：

1. **楷体 (KaiTi)** - 中文传统手写体
2. **阿里巴巴普惠体 (Alibaba PuHuiTi)** - 现代中文字体
3. **宋体 (SimSun)** - 中文标准印刷体
4. **Arial** - 英文无衬线字体
5. **Times New Roman** - 英文衬线字体

## 测试步骤

### 1. 楷体测试
选择这段文字：**这段文字将变成楷体**
- 选择文字 → 点击字体按钮 → 选择"楷体"
- 预期结果：`<span style="font-family: KaiTi">这段文字将变成楷体</span>`

### 2. 阿里巴巴普惠体测试
选择这段文字：**这段文字将变成阿里巴巴普惠体**
- 选择文字 → 点击字体按钮 → 选择"阿里巴巴普惠体"
- 预期结果：`<span style="font-family: Alibaba PuHuiTi">这段文字将变成阿里巴巴普惠体</span>`

### 3. 宋体测试
选择这段文字：**这段文字将变成宋体**
- 选择文字 → 点击字体按钮 → 选择"宋体"
- 预期结果：`<span style="font-family: SimSun">这段文字将变成宋体</span>`

### 4. Arial测试
选择这段文字：**This text will become Arial**
- 选择文字 → 点击字体按钮 → 选择"Arial"
- 预期结果：`<span style="font-family: Arial">This text will become Arial</span>`

### 5. Times New Roman测试
选择这段文字：**This text will become Times New Roman**
- 选择文字 → 点击字体按钮 → 选择"Times New Roman"
- 预期结果：`<span style="font-family: Times New Roman">This text will become Times New Roman</span>`

## 字体映射说明

在PDF生成时，字体会被映射为：

| 用户选择 | PDF中的字体 | 说明 |
|---------|------------|------|
| 楷体 | KaiTi | 如果系统有楷体字体文件 |
| 阿里巴巴普惠体 | ChineseFont | 映射到默认中文字体 |
| 宋体 | ChineseFont | 默认中文字体 |
| Arial | Helvetica | PDF标准字体 |
| Times New Roman | Times-Roman | PDF标准字体 |

## 使用方法

1. **工具栏按钮**：点击字体图标（T）
2. **快捷键**：`Ctrl+Shift+F`
3. **查看效果**：在PDF预览中查看字体变化

## 注意事项

- 字体效果只在PDF预览中显示
- 编辑器中不会显示字体变化
- 必须先选择文本才能应用字体
- 阿里巴巴普惠体在PDF中会显示为系统默认中文字体

已成功精简为5种字体！🎉
