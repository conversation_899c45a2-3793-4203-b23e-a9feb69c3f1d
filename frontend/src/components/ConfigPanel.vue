<template>
  <div class="config-panel">
    <!-- 预设配置 -->
    <div class="preset-section mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        预设配置
      </label>
      <select
        v-model="selectedPreset"
        @change="applyPreset"
        class="input-field"
      >
        <option value="">选择预设配置...</option>
        <option
          v-for="preset in presets"
          :key="preset.name"
          :value="preset.name"
        >
          {{ preset.name }} - {{ preset.description }}
        </option>
      </select>
    </div>

    <!-- 页面设置 -->
    <div class="config-section mb-6">
      <h3 class="text-sm font-semibold text-gray-900 mb-3">页面设置</h3>
      
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            页面格式
          </label>
          <select v-model="localConfig.page_format" class="input-field">
            <option value="A4">A4</option>
            <option value="A3">A3</option>
            <option value="Letter">Letter</option>
            <option value="Legal">Legal</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            DPI
          </label>
          <select v-model.number="localConfig.dpi" class="input-field">
            <option :value="150">150 DPI</option>
            <option :value="300">300 DPI</option>
            <option :value="600">600 DPI</option>
          </select>
        </div>
      </div>

      <!-- 边距设置 -->
      <div class="mt-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          页边距 (cm)
        </label>
        <div class="grid grid-cols-2 gap-2">
          <div>
            <input
              v-model.number="localConfig.margin_top"
              type="number"
              step="0.1"
              min="0"
              max="5"
              placeholder="上"
              class="input-field text-sm"
            />
          </div>
          <div>
            <input
              v-model.number="localConfig.margin_bottom"
              type="number"
              step="0.1"
              min="0"
              max="5"
              placeholder="下"
              class="input-field text-sm"
            />
          </div>
          <div>
            <input
              v-model.number="localConfig.margin_left"
              type="number"
              step="0.1"
              min="0"
              max="5"
              placeholder="左"
              class="input-field text-sm"
            />
          </div>
          <div>
            <input
              v-model.number="localConfig.margin_right"
              type="number"
              step="0.1"
              min="0"
              max="5"
              placeholder="右"
              class="input-field text-sm"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 字体设置 -->
    <div class="config-section mb-6">
      <h3 class="text-sm font-semibold text-gray-900 mb-3">字体设置</h3>
      
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            字体族
          </label>
          <select v-model="localConfig.font_family" class="input-field">
            <option
              v-for="font in availableFonts"
              :key="font.family"
              :value="font.family"
            >
              {{ font.family }}
            </option>
          </select>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              字体大小 (pt)
            </label>
            <input
              v-model.number="localConfig.font_size"
              type="number"
              min="8"
              max="24"
              step="0.5"
              class="input-field"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              行高
            </label>
            <input
              v-model.number="localConfig.line_height"
              type="number"
              min="1.0"
              max="3.0"
              step="0.1"
              class="input-field"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 段落设置 -->
    <div class="config-section mb-6">
      <h3 class="text-sm font-semibold text-gray-900 mb-3">段落设置</h3>
      
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            段落间距 (pt)
          </label>
          <input
            v-model.number="localConfig.paragraph_spacing"
            type="number"
            min="0"
            max="20"
            step="1"
            class="input-field"
          />
        </div>

        <div class="flex items-center">
          <input
            v-model="localConfig.indent_first_line"
            type="checkbox"
            id="indent-first-line"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <label for="indent-first-line" class="ml-2 text-sm text-gray-700">
            首行缩进
          </label>
        </div>

        <div class="flex items-center">
          <input
            v-model="localConfig.widow_orphan_control"
            type="checkbox"
            id="widow-orphan-control"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <label for="widow-orphan-control" class="ml-2 text-sm text-gray-700">
            孤行控制
          </label>
        </div>
      </div>
    </div>

    <!-- 印刷设置 -->
    <div class="config-section mb-6">
      <h3 class="text-sm font-semibold text-gray-900 mb-3">印刷设置</h3>
      
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            颜色模式
          </label>
          <select v-model="localConfig.color_mode" class="input-field">
            <option value="RGB">RGB (屏幕显示)</option>
            <option value="CMYK">CMYK (印刷)</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            出血 (mm)
          </label>
          <input
            v-model.number="localConfig.bleed"
            type="number"
            min="0"
            max="10"
            step="0.5"
            class="input-field"
          />
        </div>
      </div>
    </div>



    <!-- AI优化按钮 -->
    <div class="ai-optimize-section">
      <button
        @click="optimizeWithAI"
        :disabled="isOptimizing"
        class="w-full btn-primary"
      >
        <span v-if="!isOptimizing">🤖 AI智能优化</span>
        <span v-else>优化中...</span>
      </button>
      
      <div v-if="optimizationSuggestions.length > 0" class="mt-4 p-3 bg-blue-50 rounded-lg">
        <h4 class="text-sm font-medium text-blue-900 mb-2">AI建议</h4>
        <ul class="text-sm text-blue-800 space-y-1">
          <li v-for="suggestion in optimizationSuggestions" :key="suggestion">
            • {{ suggestion }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { layoutAPI } from '@/utils/api'
import type { LayoutConfig, LayoutPreset, FontInfo } from '@/types/layout'

// 组件属性
interface Props {
  config: LayoutConfig
}

const props = defineProps<Props>()

// 组件事件
const emit = defineEmits<{
  'config-updated': [config: Partial<LayoutConfig>]
}>()

// 响应式数据
const localConfig = reactive<LayoutConfig>({ ...props.config })
const selectedPreset = ref('')
const presets = ref<LayoutPreset[]>([])
const availableFonts = ref<FontInfo[]>([])
const isOptimizing = ref(false)
const optimizationSuggestions = ref<string[]>([])

// 监听本地配置变化
watch(localConfig, (newConfig) => {
  emit('config-updated', newConfig)
}, { deep: true })

// 监听外部配置变化
watch(() => props.config, (newConfig) => {
  Object.assign(localConfig, newConfig)
}, { deep: true })

// 应用预设配置
const applyPreset = () => {
  const preset = presets.value.find(p => p.name === selectedPreset.value)
  if (preset) {
    Object.assign(localConfig, preset.config)
  }
}

// AI优化
const optimizeWithAI = async () => {
  if (isOptimizing.value) return

  isOptimizing.value = true
  optimizationSuggestions.value = []

  try {
    const response = await layoutAPI.optimize({
      content: '', // 这里应该从父组件传入内容
      layout_config: localConfig,
      optimization_goals: ['readability', 'aesthetics', 'print_quality']
    })

    // 应用优化后的配置
    Object.assign(localConfig, response.optimized_config)
    optimizationSuggestions.value = response.suggestions

  } catch (error) {
    console.error('AI优化失败:', error)
  } finally {
    isOptimizing.value = false
  }
}

// 加载预设配置
const loadPresets = async () => {
  try {
    const response = await layoutAPI.getPresets()
    if (response.success && response.data) {
      presets.value = response.data
    }
  } catch (error) {
    console.error('加载预设配置失败:', error)
  }
}

// 加载可用字体 - 使用精简的5种字体列表
const loadFonts = async () => {
  // 使用精简的字体列表，只包含指定的5种字体
  availableFonts.value = [
    { name: '楷体', family: 'KaiTi', style: 'Regular', file_path: '', supports_chinese: true },
    { name: '阿里巴巴普惠体', family: 'Alibaba PuHuiTi', style: 'Regular', file_path: '', supports_chinese: true },
    { name: '宋体', family: 'SimSun', style: 'Regular', file_path: '', supports_chinese: true },
    { name: 'Arial', family: 'Arial', style: 'Regular', file_path: '', supports_chinese: false },
    { name: 'Times New Roman', family: 'Times New Roman', style: 'Regular', file_path: '', supports_chinese: false }
  ]
  console.log('配置面板使用精简字体列表:', availableFonts.value.map(f => f.name))
}

// 组件挂载时加载数据
onMounted(() => {
  loadPresets()
  loadFonts()
})
</script>

<style scoped>
.config-section {
  @apply border-b border-gray-200 pb-4;
}

.config-section:last-child {
  @apply border-b-0 pb-0;
}
</style>
