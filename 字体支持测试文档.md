# 字体支持测试文档

## 测试说明

这个文档用于测试PrintMind中精简的5种字体是否都能正常使用。请按照以下步骤进行测试：

### 1. 楷体测试

请选择下面的文字并应用楷体：

**楷体测试文字**：这段文字应该显示为楷体效果，具有传统的中文手写风格。楷体在PDF中应该能正确显示。

### 2. 阿里巴巴普惠体测试

请选择下面的文字并应用阿里巴巴普惠体：

**阿里巴巴普惠体测试文字**：这段文字应该显示为阿里巴巴普惠体效果，这是一种现代的中文字体。在PDF中会映射到默认中文字体。

### 3. 宋体测试

请选择下面的文字并应用宋体：

**宋体测试文字**：这段文字应该显示为宋体效果，这是中文的标准印刷体。在PDF中会映射到默认中文字体。

### 4. Arial测试

请选择下面的文字并应用Arial：

**Arial Test Text**: This text should be displayed in Arial font, which is a clean sans-serif font commonly used for English text.

### 5. Times New Roman测试

请选择下面的文字并应用Times New Roman：

**Times New Roman Test Text**: This text should be displayed in Times New Roman font, which is a classic serif font often used in formal documents.

## 后端字体映射

根据后端代码，这5种字体的映射关系如下：

| 前端字体选择 | PDF中的实际字体 | 说明 |
|------------|---------------|------|
| 楷体 (KaiTi) | KaiTi / STKaiti / ChineseFont | 优先使用系统楷体，不可用时使用中文字体 |
| 阿里巴巴普惠体 | ChineseFont | 映射到默认中文字体 |
| 宋体 (SimSun) | ChineseFont | 映射到默认中文字体 |
| Arial | Helvetica | PDF标准无衬线字体 |
| Times New Roman | Times-Roman | PDF标准衬线字体 |

## 字体注册情况

后端会自动注册以下字体：

### macOS系统
- **中文字体**: Arial Unicode MS, STHeiti Light
- **楷体**: simkai.ttf, STKaiti.ttc
- **粗体**: STHeiti Medium

### Windows系统
- **中文字体**: 微软雅黑, 宋体
- **楷体**: simkai.ttf
- **粗体**: 微软雅黑粗体, 黑体

### Linux系统
- **中文字体**: NotoSansCJK-Regular, DejaVuSans
- **楷体**: 文鼎楷体
- **粗体**: NotoSansCJK-Bold

## 测试步骤

1. **选择文字**: 用鼠标选择上面的测试文字
2. **打开字体选择器**: 点击工具栏字体按钮或按 `Ctrl+Shift+F`
3. **选择字体**: 从5种字体中选择一种
4. **查看效果**: 在编辑器中查看HTML标签
5. **生成PDF**: 点击"刷新"按钮生成PDF预览
6. **验证字体**: 在PDF中查看字体是否正确显示

## 预期结果

### HTML标签
选择字体后应该生成如下HTML标签：
```html
<span style="font-family: KaiTi">楷体文字</span>
<span style="font-family: Alibaba PuHuiTi">阿里巴巴普惠体文字</span>
<span style="font-family: SimSun">宋体文字</span>
<span style="font-family: Arial">Arial text</span>
<span style="font-family: Times New Roman">Times New Roman text</span>
```

### PDF显示
- **楷体**: 应该显示为楷体风格（如果系统支持）
- **阿里巴巴普惠体**: 显示为默认中文字体
- **宋体**: 显示为默认中文字体
- **Arial**: 显示为Helvetica字体
- **Times New Roman**: 显示为Times-Roman字体

## 故障排除

如果某种字体不能正常显示：

1. **检查HTML标签**: 确认生成的HTML标签正确
2. **查看后端日志**: 检查字体注册是否成功
3. **系统字体**: 确认系统中安装了相应的字体文件
4. **备选方案**: 后端会自动使用备选字体

## 结论

所有5种字体都应该能够正常使用：
- ✅ **楷体**: 支持，优先使用系统楷体
- ✅ **阿里巴巴普惠体**: 支持，映射到中文字体
- ✅ **宋体**: 支持，映射到中文字体
- ✅ **Arial**: 支持，映射到Helvetica
- ✅ **Times New Roman**: 支持，映射到Times-Roman

请按照上述步骤测试每种字体的效果！
