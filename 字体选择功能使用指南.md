# 字体选择功能使用指南

## 功能概述

PrintMind 现在支持为选中的文本应用不同的字体。这个功能允许您在同一个文档中使用多种字体，创建更丰富的排版效果。

## 如何使用

### 方法一：使用工具栏按钮

1. 在编辑器中选择您想要改变字体的文本
2. 点击工具栏中的字体图标（T形图标）
3. 从下拉菜单中选择您想要的字体
4. 选中的文本将自动被包装在字体标签中

### 方法二：使用快捷键

1. 在编辑器中选择您想要改变字体的文本
2. 按下 `Ctrl+Shift+F` (Windows/Linux) 或 `Cmd+Shift+F` (Mac)
3. 从下拉菜单中选择您想要的字体
4. 选中的文本将自动被包装在字体标签中

## 支持的字体

### 中文字体
- **楷体 (KaiTi)** - 传统的中文手写体，适合正式文档
- **宋体 (SimSun)** - 经典的中文印刷体
- **黑体 (SimHei)** - 粗体中文字体，适合标题
- **微软雅黑 (Microsoft YaHei)** - 现代无衬线中文字体

### 英文字体
- **Arial** - 现代无衬线字体，清晰易读
- **Times New Roman** - 经典衬线字体，适合正式文档

## 生成的标记

当您选择字体后，系统会自动生成如下格式的HTML标记：

```html
<span style="font-family: KaiTi">这段文字将显示为楷体</span>
```

## PDF输出

在生成PDF时，这些字体标记会被正确解析并应用相应的字体。确保您的系统中安装了相应的字体文件。

## 注意事项

1. **文本选择**：必须先选择文本才能应用字体
2. **字体可用性**：PDF输出时会使用系统中可用的字体
3. **嵌套标记**：避免在已有字体标记内再次应用字体
4. **兼容性**：生成的HTML标记与Markdown兼容

## 示例

### 混合字体文档示例

```markdown
# 标题

这是一段普通文字。<span style="font-family: KaiTi">这段文字使用楷体显示</span>，而这段文字恢复正常。

<span style="font-family: SimHei">重要提示：这段文字使用黑体显示</span>

English text with <span style="font-family: Times New Roman">Times New Roman font</span> applied.
```

## 快速上手

1. 打开 PrintMind 编辑器
2. 输入一些测试文本
3. 选择其中一部分文字
4. 点击字体按钮或按 `Ctrl+Shift+F`
5. 选择楷体
6. 查看预览效果

现在您可以创建具有丰富字体效果的文档了！
